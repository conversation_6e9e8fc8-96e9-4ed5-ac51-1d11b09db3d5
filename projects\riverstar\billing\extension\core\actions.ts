import { ObjectId } from "mongodb";
import cypressHelper from "@/app/helpers/cypress";
import h from "@/app/helpers/all";
import qbHandler from "../handlers/qbHandler";

export default {
  "Create BA Customer": function () {
    const customer =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;
    const customerData = {
      qbId: "",
      qbParentId: "",
      companyName: customer["Company Name"],
      email: customer["Email"],
      standardBillingRate: customer["Standard Billing Rate"],
      poNumber: "",
      salesAgentId: null,
      isPwv: false,
      pwvSiteList: [],
      agentVendorId: null,
      billSds: false,
      interviewAdmin: 0,
      interviewReview: 0,
      screenAdmin: 0,
      screenReview: 0,
      monitorAdmin: 0,
      monitorReview: 0,
      projectManagerId: null,
      term: "",
    };
    return Cypress.sdt.apiHandler.createCustomer(customerData);
  },
  "Sync With QB": function () {
    const entityToSync =
      Cypress.sdt.current.step.simpleValues[0]?.toLowerCase().trim() ||
      undefined;
    Cypress.sdt.apiHandler.syncWithQb(entityToSync);
  },
  "Create QB Customer": function () {
    const customer =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;
    qbHandler.createQbCustomer({
      CompanyName: customer["Company Name"],
      DisplayName: customer["Display Name"],
      Active: true,
    });
  },
  "Set QB Customer Inactive": function () {
    const customer =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;

    qbHandler.setQbCustomerInactive(customer["Display Name"]);
  },
  "Set All QB Customers Inactive": function () {
    qbHandler.setAllQbCustomersInactive();
  },
  "Set Customer PM": function () {
    const customer =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;
    const projectManager = customer["Project Manager"];

    return cy
      .task("dbReadUser", { username: projectManager["Username"] })
      .then((user) => {
        return cy
          .task("dbReadCustomer", { companyName: customer["Company Name"] })
          .then((dbCustomer) => {
            const updatedCustomerData = {
              ...(dbCustomer as object),
              projectManagerId: new ObjectId(user["_id"]),
            };
            return Cypress.sdt.apiHandler.updateCustomer(
              dbCustomer["_id"],
              updatedCustomerData
            );
          });
      });
  },
  "Create QB Employee": function () {
    const employee =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;

    qbHandler.createQbEmployee({
      DisplayName: employee["Display Name"],
      GivenName: employee["First Name"],
      FamilyName: employee["Last Name"],
    });
  },
  "Associate QB Employee to Billing User": function () {
    const employee =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;
    const billingUser = employee["Billing User"];

    let dbEmployee;
    let dbBillingUser;

    return cy
      .task("dbReadEmployee", {
        firstName: employee["First Name"],
        lastName: employee["Last Name"],
      })
      .then((value) => {
        dbEmployee = value;
      })
      .then(() =>
        cy
          .task("dbReadUser", {
            firstName: billingUser["First Name"],
            lastName: billingUser["Last Name"],
          })
          .then((value) => {
            dbBillingUser = value;
          })
      )
      .then(() =>
        Cypress.sdt.apiHandler.associateQbEmployeeToUser(
          dbEmployee,
          dbBillingUser
        )
      );
  },
  "Set All QB Employees Inactive": function () {
    qbHandler.setAllQbEmployeesInactive();
  },
  "Check QB Employees List": function () {
    qbHandler.getQbEmployees().then((employees: Array<object>) =>
      employees?.forEach((employee, index: number) => {
        cy.get("tbody tr")
          .eq(index)
          .find("td")
          .then((cells) => {
            cells[0].contains(employee["First Name"]);
            cells[1].contains(employee["Last Name"]);
          });
      })
    );
  },
  "Set All QB Vendors Inactive": function () {
    qbHandler.setAllQbVendorsInactive();
  },
  "Create QB Vendor": function () {
    const vendor =
      Cypress.sdt.current.step.simpleValues[0] ??
      Cypress.sdt.domain.getCurrentEntity;
    qbHandler.createQbEmployee({
      DisplayName: vendor["Display Name"],
      GivenName: vendor["First Name"],
      FamilyName: vendor["Last Name"],
    });
  },
  "Check QB Vendors List": function () {
    qbHandler.getQbVendors().then((vendors: Array<object>) =>
      vendors?.forEach((vendor, index: number) => {
        cy.get("tbody tr")
          .eq(index)
          .find("td")
          .then((cells) => {
            cells[0].contains(vendor["First Name"]);
            cells[1].contains(vendor["Last Name"]);
          });
      })
    );
  },
  "Delete All QB Invoices": function () {
    qbHandler.deleteAllQbInvoices();
  },
  "Select Time Slot": function () {
    const customer = Cypress.sdt.current.step.simpleValues[0];
    const customerDisplayName = customer["Display Name"];
    const dayOfWeek = Cypress.sdt.current.step.simpleValues[1];
    const dayOfWeekOffset =
      ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].indexOf(dayOfWeek) + 2;
    const selector = `div.flex-row:contains(${customerDisplayName})>div:nth-of-type(${dayOfWeekOffset})`;
    cy.get(selector).click();
  },
  "Add Activity": function () {
    const employee = Cypress.sdt.current.step.simpleValues[0];
    const customer = Cypress.sdt.current.step.simpleValues[1];
    const timeEntry = Cypress.sdt.current.step.simpleValues[2];

    if (!Cypress.sdt.domain.data.timeActivity) {
      Cypress.sdt.domain.data.timeActivity = [];
    }
    Cypress.sdt.domain.data.timeActivity.push({
      employee,
      customer,
      timeEntry,
    });
  },
  "Check BA Time Entries": function () {
    let baTimeEntries;

    cy.task("dbReadAllTimeEntries")
      .then((foundBaTimeEntries) => (baTimeEntries = foundBaTimeEntries))
      .then(() => checkBaTimeEntries(Cypress.sdt.domain.data.timeActivity));

    function checkBaTimeEntries(timeActivity) {
      cypressHelper.logTitle("Checking Billing Application Time Activity");
      cypressHelper
        .logTitle(
          "Every Time Activity is stored in the Billing Application Time Entries",
          0
        )
        .then(() => {
          timeActivity.forEach((activity) => {
            const foundBaTimeEntries = baTimeEntries.filter((baTimeEntry) => {
              const result = checkBaTimeEntry(activity, baTimeEntry);
              return result;
            });
            expect(foundBaTimeEntries).to.have.length(1);
          });
        });
      cypressHelper
        .logTitle(
          "Every Billing Application Time Entry exists in the Time Activity",
          0
        )
        .then(() => {
          baTimeEntries.forEach((baTimeEntry) => {
            const foundTimeEntries = timeActivity.filter((activity) =>
              checkBaTimeEntry(activity, baTimeEntry)
            );
            expect(foundTimeEntries).to.have.length(1);
          });
        });
    }

    function checkBaTimeEntry(activity, baTimeEntry) {
      if (
        !h.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          baTimeEntry.employee,
          activity.employee,
          "firstName"
        )
      ) {
        return false;
      }
      if (
        !h.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          baTimeEntry.employee,
          activity.employee,
          "lastName"
        )
      ) {
        return false;
      }
      if (
        !h.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          baTimeEntry.customer,
          activity.customer,
          "companyName",
          "Display Name"
        )
      ) {
        return false;
      }
      if (
        baTimeEntry.approvalUser &&
        !h.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          baTimeEntry.approvalUser,
          activity.customer,
          "User Name",
          "Project Manager.User Name"
        )
      ) {
        return false;
      }
      if (!(baTimeEntry.hours === parseInt(activity.timeEntry.Hours))) {
        return false;
      }
      if (!(baTimeEntry.minutes === parseInt(activity.timeEntry.Minutes))) {
        return false;
      }
      if (
        !h.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          baTimeEntry,
          activity.timeEntry,
          "notes"
        )
      ) {
        return false;
      }
      if (
        !(
          baTimeEntry.billable ===
          (activity.timeEntry.Chargeable.toLowerCase().trim() === "checked")
        )
      ) {
        return false;
      }

      return true;
    }
  },
  "Check QB Time Entries": function () {
    let qbTimeEntries;

    cy.then(() => qbHandler.getQbTimeEntries())
      .then((foundQbTimeEntries) => (qbTimeEntries = foundQbTimeEntries))
      .then(() => checkQbTimeEntries(Cypress.sdt.domain.data.timeActivity));

    function checkQbTimeEntries(timeActivity) {
      cypressHelper.logTitle("Checking Quick Books Time Activity");
      cypressHelper
        .logTitle(
          "Every QB Application Time Activity is a chargeable Time Activity",
          0
        )
        .then(() => {
          qbTimeEntries?.forEach((qbTimeEntry) => {
            const foundTimeEntries = timeActivity.filter((activity) =>
              checkQbTimeEntry(activity, qbTimeEntry)
            );
            expect(foundTimeEntries).to.have.length(1);
          });
        });
    }

    function checkQbTimeEntry(activity, qbTimeEntry) {
      if (
        !h.checkIfObjectsFieldsWithNormalizedKeysHaveSameValue(
          qbTimeEntry.EmployeeRef,
          activity.employee,
          "name",
          "displayName"
        )
      ) {
        return false;
      }
      if (!(qbTimeEntry.Hours === parseInt(activity.timeEntry.Hours))) {
        return false;
      }
      if (!(qbTimeEntry.Minutes === parseInt(activity.timeEntry.Minutes))) {
        return false;
      }
      if (
        !(
          qbTimeEntry.Description ===
          `${activity.employee["Display Name"]}: ${activity.timeEntry.Notes}`
        )
      ) {
        return false;
      }
      if (
        !(
          qbTimeEntry.BillableStatus ===
          (activity.timeEntry.Chargeable.toLowerCase().trim() === "checked"
            ? "Billable"
            : "NotBillable")
        )
      ) {
        return false;
      }

      return true;
    }
  },
};
